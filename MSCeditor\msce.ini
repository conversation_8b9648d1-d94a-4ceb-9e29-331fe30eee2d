// ==========================================~-
// ---> THIS FILE IS NEEDED FOR THE PROGRAM TO WORK PROPERLY <---
// ==========================================~-
// Data read in by MSCeditor, used as pre-defined teleport locations and identifiers.
// Needs to be in same folder as executable and have the file name "msce.ini".
// Add or Change entries if needed. The program barely does any validation so
// make sure the syntax is correct.
// ==========================================~-

// ==========================================~-
// These are the locations used for the map and teleporting
// Syntax:
// "DisplayName" "Coordinates" "MapRelevant"
// note : Coordinates in the format (x,(height),y)
// note : If "MapRelevant" isn't defined and "1", it's assumed to not be map relevant
// ==========================================~-

#"Locations"
"Garage Table"		"-14.3, 2, 1.5"
"Kitchen Table"		"-10, 0.3, 6"
"Garage Driveway"	"-16.2, 4, 11"
"Repair Shop"		"1553, 7, 723" 		"1"
"Teimo's Shop"		"-1554, 4, 1174"	"1"
"Ventti House"		"-173, -3, 1021"	"1"
"Drag Strip"		"-1295, 4, -940"	"1"
"Sirkka's House"	"451, 4, -1333"		"1"
"Dirt Road"			"-284, 4, -813"		"1"
"Graveyard"			"-1454, 7, 1145"	"1"
"Inspection"		"-1533, 7, 1251"	"1"
"Jokke's house"		"1944, 8, -224"		"1"
"Sewage Treatment"	"-1503, 6, 1345"	"1"
"Landfill"			"-787, 15, -645"	"1"
"Strawberry Field"	"-1206, 2, -617"	"1"
"Cottage"			"-851, 3, 513"		"1"
"Boat Spawn"		"8.7, -4.5, 149"
"Railroad Crossing" "1010, 0, -737.9"
"Sports Ground"		"-1266, 4, 1186"	"1"
"Apartments"		"-1289, 1, 1100"	"1"
"Ski Hill"			"-2026, 71, -117"	"1"
"Compost"			"0, 4, 0"
"Grain silos"		"-767, 8, 1690"		"1"
"Antenna"			"-330, 5, 1371"		"1"
"Dance Pavilion" 	"462, 10, 1333"		"1"
"Mansion"			"1360, 9, 796"		"1"
"Teimo's House"		"-877, 9, 1240"		"1"
"Home"				"-6, 0.5, 10"		"1"
"Firewood job"		"1923, 5, -420"		"1"
"Toilet Island"		"-838.5, -2, 507"	"1"
"Toilet Fleetari"	"1566, 5, 720"		"1"
"Toilet Landfill"	"-780, 13, -647"	"1"
"Toilet Teimo"		"-1549, 4, 1188"	"1"
"Septic Tank 1"		"-1300, 2, 1134"	"1"
"Septic Tank 2"		"-1357, 3, 1208"	"1"
"Septic Tank 3"		"1582, 4.5, 661"	"1"
"Septic Tank 4"		"1528, 5, 722"		"1"
"Septic Tank 5"		"1883, -3, -783"	"1"
"Woodshed"			"55, 0.5, -80"		"1"

// ==========================================~-
// These are the types of items the editor knows about!
// Syntax:
// "DisplayName" "ItemName" "attribute IDs associated with item" "layer" "ItemIDName"
// note : ItemIDName defaults to ItemName + "ID" when not set
// note : All items have the 0:Transform attribute
// ==========================================~-

#"Items"
"Alternator belt"		"alternator belt"	"26, 28"					"PART"	"alternatorbeltID"
"Battery"				"battery"			"1, 26, 29, 30, 31"			"PART"	
"Beer case"				"beercase"			"1, 2"						"PART"	"BeerCaseID"
"Booze"					"booze"				"1"							"PART"	"BoozeID"
"Brake fluid"			"brakefluid"		"1, 3"						"PART"
"Cigarettes"			"cigarettes"		"1"							"PART"
"Coffee"				"groundcoffee"		"34"						"PART"
"Coolant"				"coolant"			"1, 6"						"PART"
"Empty plastic can"		"juiceconcentrate"	"1, 14, 15, 16, 17, 18, 19"	"ITEM"	 
"Fire extinguisher"		"fireextinguisher"	"1, 4, 26"					"ITEM"
"Grill charcoal"		"grillcharcoal"		"35"						"PART"
"Juice concentrate"		"juiceconcentrate"	"1, 8, 9, 10, 11, 12, 13" 	"ITEM"
"Kilju"					"juiceconcentrate"	"1, 20, 21, 22, 23, 24, 25" "ITEM"
"Macaron box"			"macaronbox"		"1"							"PART"
"Milk"					"milk"				"1"							"PART"
"Moosemeat (Grilled)"	"moosemeat"			"36, 37"					"PART"
"Mosquitospray"			"mosquitospray"		"1, 4"						"PART"
"Motoroil"				"motoroil"			"1, 5"						"PART"
"Oil filter"			"oil filter"		"1, 26, 27, 32"				"PART"	"oilfilterID"
"Pike (Grilled)"		"pike"				"36, 37"					"PART"
"Pizza"					"pizza"				"1"							"PART"
"Potatochips"			"potatochips"		"1"							"PART"
"Sausages"				"sausages" 			"1"							"PART"
"Box of sparkplugs"		"spark plug box"	"33"						"PART"	"sparkplugboxID"
"Sugar"					"sugar"				"1"							"PART"
"Two stroke fuel"		"twostroke"			"1, 7"						"PART"
"Yeast"					"yeast"				"1"							"PART"


// ==========================================~-
// Item attributes
// Syntax:
// "attribute ID" "name" "datatype id"  "min" "max"
// datatypes: 
// ID_ARRAY			0
// ID_TRANSFORM		1
// ID_FLOAT			2
// ID_STRING		3
// ID_BOOL			4
// ID_COLOR			5
// ID_INT			6
// ==========================================~-

#"Item_Attributes"
// All items have the transform attribute
"0"	 "Transform"		"1"

// A consumed item will not spawn
"1"	 "Consumed"			"4"		"1"		"0"
"2"	 "Bottles"			"6"		"18"	"0"
"3"	 "Fluid"			"2"		"0"		"1"
"4"	 "Fluid"			"2"		"0"		"100"
"5"	 "Fluid"			"2"		"0"		"4"
"6"	 "Fluid"			"2"		"0"		"10"
"7"	 "Fluid"			"2"		"0"		"5"

// Juice
"8"	 "ContainsKilju"	"4"		"-1"	"0"
"9"	 "ContainsJuice"	"4"		"0"		"1"
"10" "KiljuYeast"		"2"		"-1"	"0"
"11" "KiljuVinegar"		"2"		"-1"	"0"
"12" "KiljuSweetness"	"2"		"-1"	"0"
"13" "KiljuAlc"			"2"		"-1"	"0"

// Empty juice
"14" "ContainsKilju"	"4"		"-1"	"0"
"15" "ContainsJuice"	"4"		"-1"	"0"
"16" "KiljuYeast"		"2"		"-1"	"0"
"17" "KiljuVinegar"		"2"		"-1"	"0"
"18" "KiljuSweetness"	"2"		"-1"	"0"
"19" "KiljuAlc"			"2"		"-1"	"0"

// Kilju
"20" "ContainsKilju"	"4"		"-1"	"1"
"21" "ContainsJuice"	"4"		"-1"	"0"
"22" "KiljuYeast"		"2"		"-1"	"1"
"23" "KiljuVinegar"		"2"		"-1"	"0"
"24" "KiljuSweetness"	"2"		"-1"	"0.2"
"25" "KiljuAlc"			"2"		"-1"	"0.1362"

// Items that can be installed in the car
// We'll keep both states at false for now
"26" "Installed"		"4"		"0"		"0"
"27" "Tightness"		"2"		"0"		"0"
"28" "Wear"				"2"		"100"	"0"

// Battery 
"29" "Discharge"		"2"		"140"	"0"
"30" "Charge"			"2"		"0"		"140"
"31" "OnCharged"		"4"		"0"		"0"

// Oil filter
"32" "Dirt"				"2"		"100"	"1"

// Sparkplugs
"33" "Quantity"			"6"		"0"		"4"

// Coffee
"34" "Ground"			"2"		"0"		"100"

// Charcoal
"35" "Contents"			"2"		"0"		"100"

// Moose Meat
"36" "Decay"			"2"		"1"		"0"
"37" "Type"				"6"		"3"		"2"

// ==========================================~-
// These are used by the Satsuma report to identify car parts properly. 
// If the report function doesn't work, that means the game probably 
// received an update that changed how the variables are named. 
// You can probably fix this yourself by changing the keys here
// ==========================================~-

#"Report_Identifiers"
"bolted" // bolted , a bool where I'm not sure how it works, but it's probably important. 
"bolts" // bolts , a stringlist keeping track of all bolt states on a part. Only exists when the part has bolts, and is installed
"damaged" //damaged , a bool saving the damage state of a part
"installed" //installed , a bool used by the tool to identify a carpart
"tightness" //tightness , a float that is the sum of all bolt states on a part. Only exists when the part has bolts
"corner" //carcorner where wheel is installed , empty string when not installed, otherwise it's FR/FL/RR/RL

// ==========================================~-
// Some special cases for the Satsuma report
// "string to match" "SCid" "param"(optional)
// SCids:
// 0 : tightness offset, by boltstates
// 1 : 
// 2 : hide entries in report
// ==========================================~-

#"Report_Special"
"oilpan" "0" "-8" //big bolt at the bottom of the pan doesn't count towards tightness because reasons
"alternator" "0" "-8" //alternator rotation doesn't count towards tightness
//"alternator"  "1" "alternatorrotation"
"motorhoist" "2" 
"fiberglasshood" "2" 
"warningtriangle" "2" 

// ==========================================~-
// Maintenance entries.
// Entries not in this list containing "wear"
// in their name will be added by the program.
// Asterisks are wildcards for numbers, used for items
// Syntax:
// "Display name" "name" "data type" "min" "max" "suggestion"
// datatypes: 
// ID_FLOAT			2
// ID_VECTOR		7
// ==========================================~-

#"Report_Maintenance"
"Fuel level"					"fueltankfuellevel"					"2"		"0"			"36"
"Engine oil level"				"oilpanoillevel"					"2"		"0"			"3"	
"Oil condition"					"oilpanoilcontamination"			"2"		"95"		"0"	
"Front brakefluid-level"		"brakemastercylinderfluidlevelf"	"2"		"0"			"1"
"Rear brakefluid-level"			"brakemastercylinderfluidlevelr"	"2"		"0"			"1"
"Clutch fluid-level"			"clutchmastercylinderfluidlevel"	"2"		"0"			"0.5"
"Racing radiator coolant level"	"racingradiatorwater"				"2"		"0"			"7"
"Stock radiator coolant level"	"radiatorwater"						"2"		"0"			"7"			// todo: determine max
"Camshaft alignment"			"camshaftgearangle"					"2"		"0"			"0"			"0"
"Front left wheel alignment"	"steeringrodflalignment"			"2"		"0"			"0"			"0"
"Front right wheel alignment"	"steeringrodfralignment"			"2"		"0"			"0"			"0"
"Cylinder intake 1"				"rockershaftcyl1intake"				"2"		"7.75"		"8"			"8"
"Cylinder intake 2"				"rockershaftcyl2intake"				"2"		"7.75"		"8"			"8"
"Cylinder intake 3"				"rockershaftcyl3intake"				"2"		"7.75"		"8"			"8"
"Cylinder intake 4"				"rockershaftcyl4intake"				"2"		"7.75"		"8"			"8"
"Cylinder exhaust 1"			"rockershaftcyl1exhaust"			"2"		"6.75"		"8"			"7"
"Cylinder exhaust 2"			"rockershaftcyl2exhaust"			"2"		"6.75"		"8"			"7"
"Cylinder exhaust 3"			"rockershaftcyl3exhaust"			"2"		"6.75"		"8"			"7"
"Cylinder exhaust 4"			"rockershaftcyl4exhaust"			"2"		"6.75"		"8"			"7"
"Stock carburator A/F ratio"	"carburatoridleadjust"				"2"		"10"		"16.75"		"14.7"
"Twin carburators A/F ratio"	"twincarburatorsidleadjust"			"2"		"10"		"16.75"		"14.7"
"Racing carburator A/F ratio 1"	"racingcarburatorsadjust1"			"2"		"10"		"17"		"14.9"
"Racing carburator A/F ratio 2"	"racingcarburatorsadjust2"			"2"		"10"		"17"		"14.9"
"Racing carburator A/F ratio 3"	"racingcarburatorsadjust3"			"2"		"10"		"17"		"14.9"
"Racing carburator A/F ratio 4"	"racingcarburatorsadjust4"			"2"		"10"		"17"		"14.9"
"Final gear ratio"				"gearboxfinalgearratio"				"2"		""			"3.7"
"Distributor spark timing"		"distributorsparkangle"				"2"		"12"		"15"		"14.5"
"Fanbelt tightness"				"alternatorrotation"				"2"		"7"			"7"			"7"
"Suspension Condition FL"		"wheelfldamagevector"				"7"		""			""			"-0, 0, 0"		
"Suspension Condition FR"		"wheelfrdamagevector"				"7"		""			""			"-0, 0, 0"	
"Suspension Condition RL"		"wheelrldamagevector"				"7"		""			""			"-0, 0, 0"		
"Suspension Condition RR"		"wheelrrdamagevector"				"7"		""			""			"-0, 0, 0"	
"Fanbelt condition (deprecated)""fanbeltwear"						"2"		"100"		"0"
"Fanbelt condition"				"alternatorbelt*wear"				"2"		"100"		"0"
"Battery charge"				"battery*charge"					"2"		"0"			"140"
"Battery capacity"				"battery*chargemax"					"2"		"0"			"145"
"Oilfilter dirt"				"oilfilter*dirt"					"2"		"100"		"1"
"Oilfilter tightness"			"oilfilter*tightness"				"2"		"0"			"8"
"Fireextinguisher content"		"fireextinguisher*fluid"			"2"		"0"			"100"

// ==========================================~-
// Event Timetable
// ==========================================~-

#"Event_Timetable"
"Jokke ride call"	"02 - 03"		"Any"
"Sunrise"			"06 - 07"		"Any"
"Jokke kilju hours"	"06 - 22"		"Any"
"Repair shop"		"08 - 16"		"Mon - Fri"
"Inspection Office"	"08 - 16"		"Mon - Fri"
"Sewage Treatment"	"08 - 16"		"Mon - Fri"
"Grandma Tea Time"	"08 - 16"		"Any"
"Rally 1st day"		"10 - 18"		"Sat"
"Rally 2nd day"		"10 - 18"		"Sun"
"Teimo's Shop"		"10 - 20"		"Mon - Sat"
"Soccer kids"		"10 - 22"		"Any"
"Dance Pavilion"	"20 - 02"		"Sat"
"Pub Nappo"			"20 - 02"		"Mon - Sat"
"Sunset"			"22 - 23"		"Any"

// ==========================================~-
// Settings
// ==========================================~-

#"Settings"
"make_backup" "1"
"backup_change_notified" "1"
"check_updates" "1"
"first_startup" "0"
"allow_scale" "0"
"use_euler" "0"
"raw_names" "0"
"check_issues" "0"
"start_with_map" "1"
