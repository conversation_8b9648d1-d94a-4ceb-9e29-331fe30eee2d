#include "stdint.h"


#define D<PERSON>LOGRESIZECONTR<PERSON><PERSON>ASSW L"DialogResizeControlClass<PERSON>"
#define DIALOGRESIZECONTROLCLASSA "DialogResizeControlClassA"

#if defined(UNICODE) || defined(_UNICODE)
#define DIAL<PERSON>GRESIZECONTROLCLASS DIALOGRESIZECONTR<PERSON>CLASSW
#else
#define DIAL<PERSON>GRESIZECONTROLCLASS DIALOGRESIZECONTROLCLASSA
#endif

#define DIALOGRESIZEDATACLASSW L"DialogResizeDataClassW"
#define DIALOGRESIZEDATACLASSA "DialogResizeDataClassA"

#if defined(UNICODE) || defined(_UNICODE)
#define DIALOGRESIZEDATACLASS DIALOGRESIZEDATACLASSW
#else
#define DIALOGRESIZEDATACLASS DIALOGRESIZEDATACLASSA
#endif

#ifndef RC_INVOKED

BOOL CALLBACK
ResizeDialogProc(<PERSON><PERSON><PERSON> hDlg, uint32_t uMsg, <PERSON><PERSON><PERSON><PERSON> wParam, <PERSON><PERSON><PERSON> lParam, PVOID * ppStorage);

BOOL
ResizeDialogInitialize(HINSTANCE hInst);

#else

#define DIALOGRESIZE CONTROL "", -1, DIALOGRESIZEDATACLASS, NOT WS_VISIBLE, 0, 0, 0, 0, 0, 
#define DIALOGRESIZECONTROL CONTROL "", -1, DIALOGRESIZECONTROLCLASS, NOT WS_VISIBLE, 0, 0, 0, 0, 0, 

#endif

